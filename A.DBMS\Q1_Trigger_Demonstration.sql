-- =============================================
-- Q1: Price Drop Prevention Trigger - DEMONSTRATION
-- AdventureWorks Database Exercise
-- =============================================

USE AdventureWorks2022;
GO

PRINT '=============================================';
PRINT 'DEMONSTRATION: Price Drop Prevention Trigger';
PRINT '=============================================';
PRINT '';

-- =============================================
-- STEP 1: Show current product prices (sample data)
-- =============================================
PRINT 'STEP 1: Current Product Prices (Sample Data)';
PRINT '---------------------------------------------';

SELECT TOP 5
    ProductID,
    Name,
    ListPrice,
    ProductNumber
FROM Production.Product
WHERE ListPrice > 0
ORDER BY ListPrice DESC;

PRINT '';
PRINT 'Let''s work with specific products for our demonstration...';
PRINT '';

-- =============================================
-- STEP 2: Select specific products for testing
-- =============================================
DECLARE @TestProductID1 INT, @TestProductID2 INT;
DECLARE @CurrentPrice1 MONEY, @CurrentPrice2 MONEY;

-- Get two products with different price ranges
SELECT TOP 1 @TestProductID1 = ProductID, @CurrentPrice1 = ListPrice
FROM Production.Product
WHERE ListPrice BETWEEN 100 AND 500
ORDER BY ProductID;

SELECT TOP 1 @TestProductID2 = ProductID, @CurrentPrice2 = ListPrice
FROM Production.Product
WHERE ListPrice BETWEEN 1000 AND 2000
ORDER BY ProductID;

PRINT 'Test Products Selected:';
SELECT
    ProductID,
    Name,
    ListPrice,
    'Test Product 1' AS TestRole
FROM Production.Product
WHERE ProductID = @TestProductID1

UNION ALL

SELECT
    ProductID,
    Name,
    ListPrice,
    'Test Product 2' AS TestRole
FROM Production.Product
WHERE ProductID = @TestProductID2;

PRINT '';

-- =============================================
-- STEP 3: Test Case 1 - Valid Price Increase
-- =============================================
PRINT 'STEP 3: Test Case 1 - Valid Price Increase';
PRINT '-------------------------------------------';
PRINT 'Attempting to INCREASE price (should succeed)...';

BEGIN TRY
    UPDATE Production.Product
    SET ListPrice = @CurrentPrice1 + 50.00
    WHERE ProductID = @TestProductID1;

    PRINT '✓ SUCCESS: Price increase allowed!';

    -- Show the updated price
    SELECT
        ProductID,
        Name,
        ListPrice AS NewPrice,
        @CurrentPrice1 AS OldPrice,
        ListPrice - @CurrentPrice1 AS PriceChange
    FROM Production.Product
    WHERE ProductID = @TestProductID1;

END TRY
BEGIN CATCH
    PRINT '✗ UNEXPECTED ERROR: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- =============================================
-- STEP 4: Test Case 2 - Invalid Price Decrease
-- =============================================
PRINT 'STEP 4: Test Case 2 - Invalid Price Decrease';
PRINT '---------------------------------------------';
PRINT 'Attempting to DECREASE price (should fail)...';

BEGIN TRY
    UPDATE Production.Product
    SET ListPrice = @CurrentPrice2 - 100.00
    WHERE ProductID = @TestProductID2;

    PRINT '✗ UNEXPECTED: Price decrease was allowed (this should not happen)!';

END TRY
BEGIN CATCH
    PRINT '✓ SUCCESS: Price decrease prevented by trigger!';
    PRINT 'Error Message: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- =============================================
-- STEP 5: Test Case 3 - Multiple Row Update with Mixed Changes
-- =============================================
PRINT 'STEP 5: Test Case 3 - Multiple Row Update (Mixed Price Changes)';
PRINT '----------------------------------------------------------------';
PRINT 'Attempting to update multiple products with both increases and decreases...';

BEGIN TRY
    -- This should fail because one product has a price decrease
    UPDATE Production.Product
    SET ListPrice = CASE
        WHEN ProductID = @TestProductID1 THEN ListPrice + 25.00  -- Increase
        WHEN ProductID = @TestProductID2 THEN ListPrice - 50.00  -- Decrease (should trigger error)
        ELSE ListPrice
    END
    WHERE ProductID IN (@TestProductID1, @TestProductID2);

    PRINT '✗ UNEXPECTED: Mixed price changes were allowed!';

END TRY
BEGIN CATCH
    PRINT '✓ SUCCESS: Mixed update prevented due to price decrease!';
    PRINT 'Error Message: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- =============================================
-- STEP 6: Test Case 4 - Edge Cases
-- =============================================
PRINT 'STEP 6: Test Case 4 - Edge Cases';
PRINT '---------------------------------';

-- Test with NULL values
PRINT 'Testing updates that don''t affect ListPrice (should succeed)...';

BEGIN TRY
    UPDATE Production.Product
    SET ModifiedDate = GETDATE()
    WHERE ProductID = @TestProductID1;

    PRINT '✓ SUCCESS: Non-price updates are allowed!';
END TRY
BEGIN CATCH
    PRINT '✗ ERROR: ' + ERROR_MESSAGE();
END CATCH

-- Test setting same price (should succeed)
PRINT 'Testing setting the same price (should succeed)...';

BEGIN TRY
    DECLARE @SamePrice MONEY;
    SELECT @SamePrice = ListPrice FROM Production.Product WHERE ProductID = @TestProductID1;

    UPDATE Production.Product
    SET ListPrice = @SamePrice
    WHERE ProductID = @TestProductID1;

    PRINT '✓ SUCCESS: Setting same price is allowed!';
END TRY
BEGIN CATCH
    PRINT '✗ ERROR: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- =============================================
-- STEP 7: Restore Original Prices
-- =============================================
PRINT 'STEP 7: Restoring Original Prices';
PRINT '----------------------------------';

BEGIN TRY
    -- Restore original prices (increases are allowed)
    UPDATE Production.Product
    SET ListPrice = @CurrentPrice1
    WHERE ProductID = @TestProductID1;

    PRINT '✓ Test Product 1 price restored to original value.';
END TRY
BEGIN CATCH
    PRINT '✗ Error restoring price: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- =============================================
-- STEP 8: Final Summary
-- =============================================
PRINT 'STEP 8: Business Logic Summary';
PRINT '------------------------------';
PRINT 'The Price Drop Prevention Trigger successfully:';
PRINT '• ✓ Allows price increases';
PRINT '• ✓ Allows setting the same price';
PRINT '• ✓ Allows non-price column updates';
PRINT '• ✓ Prevents any price decreases';
PRINT '• ✓ Provides detailed error messages';
PRINT '• ✓ Handles multiple row updates correctly';
PRINT '• ✓ Handles NULL values appropriately';
PRINT '';
PRINT 'Business Rule Enforced: Product prices can only stay the same or increase,';
PRINT 'never decrease, protecting against accidental price reductions.';

PRINT '';
PRINT '=============================================';
PRINT 'DEMONSTRATION COMPLETED';
PRINT '=============================================';
GO