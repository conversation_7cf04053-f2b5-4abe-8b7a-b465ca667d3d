# Q1: Trigger to Prevent Price Drop - AdventureWorks Database

## Overview
This solution implements a database trigger that prevents price reductions on products in the AdventureWorks database. The trigger ensures that the `ListPrice` in the `Production.Product` table can only stay the same or increase, never decrease.

## Business Logic
**Rule**: Product prices can only be maintained or increased, never reduced.

**Rationale**: This business rule protects against:
- Accidental price reductions
- Unauthorized discounting
- Data entry errors that could impact revenue
- Maintaining price integrity in the system

## Files Included

### 1. `Q1_Price_Drop_Prevention_Trigger.sql`
- **Purpose**: Creates the main trigger `trg_PreventPriceDrop`
- **Functionality**: 
  - Monitors UPDATE operations on `Production.Product`
  - Checks if `ListPrice` is being reduced
  - Prevents the update and provides detailed error messages
  - Handles NULL values appropriately

### 2. `Q1_Trigger_Demonstration.sql`
- **Purpose**: Comprehensive demonstration of the trigger functionality
- **Test Cases**:
  - Valid price increases (should succeed)
  - Invalid price decreases (should fail)
  - Multiple row updates with mixed changes
  - Edge cases (same price, non-price updates)
  - NULL value handling

## How to Run

### Step 1: Create the Trigger
```sql
-- Execute the trigger creation script
-- This will create the trg_PreventPriceDrop trigger
```

### Step 2: Run the Demonstration
```sql
-- Execute the demonstration script
-- This will show various test cases and their results
```

## Trigger Details

### Trigger Name: `trg_PreventPriceDrop`
- **Type**: AFTER UPDATE trigger
- **Table**: `Production.Product`
- **Event**: Fires after UPDATE operations

### Key Features:
1. **Selective Monitoring**: Only activates when `ListPrice` column is updated
2. **Detailed Error Messages**: Provides specific information about which products and prices caused the violation
3. **Transaction Safety**: Uses `ROLLBACK TRANSACTION` to prevent partial updates
4. **NULL Handling**: Properly handles NULL values in price comparisons
5. **Multi-row Support**: Works correctly with updates affecting multiple products

### Logic Flow:
1. Check if `ListPrice` column was updated using `UPDATE(ListPrice)`
2. Compare new prices (`inserted`) with old prices (`deleted`)
3. If any new price is lower than the corresponding old price:
   - Build detailed error message with product information
   - Rollback the entire transaction
   - Raise an informative error

## Test Scenarios Covered

### ✅ Allowed Operations:
- Price increases
- Setting the same price
- Updates to other columns (non-price)
- Updates where ListPrice is NULL

### ❌ Prevented Operations:
- Any price decrease
- Mixed updates where at least one product has a price decrease

## Error Message Format
```
Price drop prevention: Cannot reduce ListPrice below current value. 
Affected products: ProductID: 123, Product: Sample Product, 
Current Price: $100.00, Attempted New Price: $90.00;
```

## Business Impact
This trigger ensures:
- **Data Integrity**: Prevents accidental price reductions
- **Revenue Protection**: Maintains pricing discipline
- **Audit Trail**: Clear error messages for troubleshooting
- **System Reliability**: Consistent price management across the application

## Technical Considerations
- **Performance**: Minimal impact as it only fires on UPDATE operations
- **Concurrency**: Works correctly with concurrent transactions
- **Maintenance**: Easy to disable/enable if needed for bulk operations
- **Extensibility**: Can be modified to add additional business rules

## Usage Examples

### Successful Price Increase:
```sql
UPDATE Production.Product 
SET ListPrice = 150.00 
WHERE ProductID = 1 AND ListPrice = 100.00;
-- ✅ Success: Price increased from $100 to $150
```

### Prevented Price Decrease:
```sql
UPDATE Production.Product 
SET ListPrice = 80.00 
WHERE ProductID = 1 AND ListPrice = 100.00;
-- ❌ Error: Price drop prevention triggered
```

## Maintenance Notes
- To temporarily disable: `DISABLE TRIGGER trg_PreventPriceDrop ON Production.Product`
- To re-enable: `ENABLE TRIGGER trg_PreventPriceDrop ON Production.Product`
- To drop: `DROP TRIGGER trg_PreventPriceDrop`
