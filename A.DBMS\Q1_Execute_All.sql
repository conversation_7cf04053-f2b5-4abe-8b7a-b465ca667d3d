-- =============================================
-- Q1: Complete Execution Script
-- Price Drop Prevention Trigger - AdventureWorks
-- =============================================

PRINT '=============================================';
PRINT 'Q1: PRICE DROP PREVENTION TRIGGER';
PRINT 'AdventureWorks Database Exercise';
PRINT '=============================================';
PRINT '';

-- Ensure we're using the correct database
USE AdventureWorks2022;
GO

PRINT 'Step 1: Creating the Price Drop Prevention Trigger...';
PRINT '----------------------------------------------------';

-- =============================================
-- STEP 1: Create the Trigger
-- =============================================

-- Drop the trigger if it already exists
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'trg_PreventPriceDrop')
BEGIN
    DROP TRIGGER trg_PreventPriceDrop;
    PRINT 'Existing trigger dropped.';
END
GO

-- Create the trigger
CREATE TRIGGER trg_PreventPriceDrop
ON Production.Product
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Check if ListPrice column was updated
    IF UPDATE(ListPrice)
    BEGIN
        -- Check for any rows where new price is less than old price
        IF EXISTS (
            SELECT 1 
            FROM inserted i
            INNER JOIN deleted d ON i.ProductID = d.ProductID
            WHERE i.ListPrice < d.ListPrice
                AND d.ListPrice IS NOT NULL  -- Handle NULL values
                AND i.ListPrice IS NOT NULL
        )
        BEGIN
            -- Get details of the problematic updates for error message
            DECLARE @ErrorDetails NVARCHAR(MAX) = '';
            
            SELECT @ErrorDetails = @ErrorDetails + 
                'ProductID: ' + CAST(i.ProductID AS NVARCHAR(10)) + 
                ', Product: ' + ISNULL(i.Name, 'Unknown') +
                ', Current Price: $' + CAST(d.ListPrice AS NVARCHAR(20)) + 
                ', Attempted New Price: $' + CAST(i.ListPrice AS NVARCHAR(20)) + '; '
            FROM inserted i
            INNER JOIN deleted d ON i.ProductID = d.ProductID
            WHERE i.ListPrice < d.ListPrice
                AND d.ListPrice IS NOT NULL
                AND i.ListPrice IS NOT NULL;
            
            -- Rollback the transaction and raise an error
            ROLLBACK TRANSACTION;
            
            DECLARE @ErrorMessage NVARCHAR(MAX) = 
                'Price drop prevention: Cannot reduce ListPrice below current value. ' +
                'Affected products: ' + @ErrorDetails;
            
            RAISERROR(@ErrorMessage, 16, 1);
            RETURN;
        END
    END
END;
GO

PRINT '✓ Trigger "trg_PreventPriceDrop" created successfully!';
PRINT '';

-- Verify trigger creation
SELECT 
    t.name AS TriggerName,
    t.type_desc AS TriggerType,
    t.is_disabled AS IsDisabled,
    OBJECT_NAME(t.parent_id) AS TableName
FROM sys.triggers t
WHERE t.name = 'trg_PreventPriceDrop';

PRINT '';
PRINT 'Step 2: Running Comprehensive Demonstration...';
PRINT '----------------------------------------------';
PRINT '';

-- =============================================
-- STEP 2: Run the Demonstration
-- =============================================

-- Include the demonstration script content here
-- (The demonstration script will be executed next)

PRINT 'Trigger creation completed successfully!';
PRINT 'Please run Q1_Trigger_Demonstration.sql to see the trigger in action.';
PRINT '';
PRINT 'Files created:';
PRINT '• Q1_Price_Drop_Prevention_Trigger.sql - Trigger creation script';
PRINT '• Q1_Trigger_Demonstration.sql - Comprehensive demonstration';
PRINT '• Q1_README.md - Complete documentation';
PRINT '• Q1_Execute_All.sql - This execution script';
GO
