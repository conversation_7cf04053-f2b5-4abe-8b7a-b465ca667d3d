-- =============================================
-- SIMPLE TEST FOR Q1 ADBMS LAB TRIGGER
-- Quick and easy testing of PreventPriceDrop trigger
-- =============================================

USE AdventureWorks2022;
GO

PRINT 'Simple Test for PreventPriceDrop Trigger';
PRINT '========================================';

-- Check if trigger exists
IF NOT EXISTS (SELECT * FROM sys.triggers WHERE name = 'PreventPriceDrop')
BEGIN
    PRINT 'ERROR: Trigger not found! Please create the trigger first.';
    RETURN;
END

PRINT 'Trigger found! Starting tests...';
PRINT '';

-- Get a test product
DECLARE @ProductID INT = 1;  -- Using ProductID 1 for simplicity
DECLARE @CurrentPrice MONEY;

SELECT @CurrentPrice = ListPrice 
FROM Production.Product 
WHERE ProductID = @ProductID;

PRINT 'Test Product Info:';
SELECT ProductID, Name, ListPrice 
FROM Production.Product 
WHERE ProductID = @ProductID;

PRINT '';

-- TEST 1: Try to INCREASE price (should work)
PRINT 'TEST 1: Increasing price (should succeed)';
PRINT '-----------------------------------------';

BEGIN TRY
    UPDATE Production.Product 
    SET ListPrice = @CurrentPrice + 10.00
    WHERE ProductID = @ProductID;
    
    PRINT '✓ SUCCESS: Price increase allowed';
    
    -- Show new price
    SELECT 'New Price' AS Status, ListPrice 
    FROM Production.Product 
    WHERE ProductID = @ProductID;
    
END TRY
BEGIN CATCH
    PRINT '✗ ERROR: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- TEST 2: Try to DECREASE price (should fail)
PRINT 'TEST 2: Decreasing price (should fail)';
PRINT '--------------------------------------';

BEGIN TRY
    UPDATE Production.Product 
    SET ListPrice = @CurrentPrice - 5.00
    WHERE ProductID = @ProductID;
    
    PRINT '✗ PROBLEM: Price decrease was allowed (trigger not working?)';
    
END TRY
BEGIN CATCH
    PRINT '✓ SUCCESS: Price decrease prevented!';
    PRINT 'Error: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- Restore original price
UPDATE Production.Product 
SET ListPrice = @CurrentPrice
WHERE ProductID = @ProductID;

PRINT 'Original price restored.';
PRINT 'Test completed!';
GO
