USE AdventureWorks2022;
GO

-- Q1: Trigger to Prevent Price Drop

CREATE TRIGGER PreventPriceDrop
ON Production.Product
FOR UPDATE
AS
BEGIN
    IF EXISTS (
        SELECT 1 FROM inserted i
        JOIN deleted d ON i.ProductID = d.ProductID
        WHERE i.ListPrice < d.ListPrice
    )
    BEGIN
        RAISERROR ('Price reduction is not allowed!', 16, 1);
        ROL<PERSON><PERSON>CK TRANSACTION;
    END
END;


SELECT * FROM Production.Product;

SELECT ProductID, Name, ListPrice FROM Production.Product WHERE ProductID = 515;

UPDATE Production.Product
SET ListPrice = 140
WHERE ProductID = 515;

GO

--Q2: Stored Procedure to Update Employee Email 

CREATE PROCEDURE UpdateEmployeeEmail
    @BusinessEntityID INT,
    @NewEmailAddress NVARCHAR(50)
AS
BEGIN
    IF NOT EXISTS (SELECT 1 FROM Person.EmailAddress WHERE BusinessEntityID = @BusinessEntityID)
    BEGIN
        PRINT 'Error: Employee not found!';
        R<PERSON><PERSON><PERSON>;
    E<PERSON>

    UPDATE Person.EmailAddress
    SET EmailAddress = @NewEmailAddress
    WHERE BusinessEntityID = @BusinessEntityID;

    PRINT 'Email address updated successfully!';
END;

GO

SELECT * FROM Person.EmailAddress;

GO

EXEC UpdateEmployeeEmail 2, '<EMAIL>';

GO

--Q3: Trigger to Audit Product Updates 

CREATE TABLE ProductPriceAudit (
    AuditID INT IDENTITY(1,1) PRIMARY KEY,
    ProductID INT,
    OldPrice DECIMAL(10,2),
    NewPrice DECIMAL(10,2),
    ChangeDate DATETIME DEFAULT GETDATE(),
    ModifiedBy NVARCHAR(100)
);

GO

CREATE or ALTER TRIGGER AuditProductPriceUpdate
ON Production.Product
FOR UPDATE
AS
BEGIN
    INSERT INTO ProductPriceAudit (ProductID, OldPrice, NewPrice, ChangeDate, ModifiedBy)
    SELECT d.ProductID, d.ListPrice, i.ListPrice, GETDATE(), SUSER_NAME()
    FROM inserted i
    JOIN deleted d ON i.ProductID = d.ProductID
    WHERE i.ListPrice != d.ListPrice; 
END;


GO

UPDATE Production.Product
SET ListPrice = 100
WHERE ProductID = 2;

GO

SELECT ListPrice FROM Production.Product;

SELECT * FROM ProductPriceAudit;

GO

--Q4: Stored Procedure to Insert a New Product

CREATE PROCEDURE InsertNewProduct
    @Name NVARCHAR(50),
    @ProductNumber NVARCHAR(25),
    @Color NVARCHAR(15),
    @StandardCost DECIMAL(10,2),
    @ListPrice DECIMAL(10,2),
    @SellStartDate DATETIME
AS
BEGIN
    INSERT INTO Production.Product (Name, ProductNumber, Color, StandardCost, ListPrice, SellStartDate)
