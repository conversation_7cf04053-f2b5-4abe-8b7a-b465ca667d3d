-- =============================================
-- TESTING SCRIPT FOR Q1 ADBMS LAB TRIGGER
-- Testing the PreventPriceDrop trigger
-- =============================================

USE AdventureWorks2022;
GO

PRINT '=============================================';
PRINT 'TESTING Q1 ADBMS LAB TRIGGER: PreventPriceDrop';
PRINT '=============================================';
PRINT '';

-- =============================================
-- STEP 1: Verify the trigger exists
-- =============================================
PRINT 'STEP 1: Checking if trigger exists...';
PRINT '-------------------------------------';

IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'PreventPriceDrop')
BEGIN
    PRINT '✓ Trigger "PreventPriceDrop" found!';
    
    SELECT 
        t.name AS TriggerName,
        t.type_desc AS TriggerType,
        t.is_disabled AS IsDisabled,
        OBJECT_NAME(t.parent_id) AS TableName,
        t.create_date AS CreatedDate
    FROM sys.triggers t
    WHERE t.name = 'PreventPriceDrop';
END
ELSE
BEGIN
    PRINT '✗ Trigger "PreventPriceDrop" NOT found!';
    PRINT 'Please run your Q1 ADBMS lab.sql file first to create the trigger.';
    RETURN;
END

PRINT '';

-- =============================================
-- STEP 2: Select test products
-- =============================================
PRINT 'STEP 2: Selecting test products...';
PRINT '-----------------------------------';

-- Find products with prices for testing
DECLARE @TestProduct1 INT, @TestProduct2 INT;
DECLARE @OriginalPrice1 MONEY, @OriginalPrice2 MONEY;

-- Get first test product
SELECT TOP 1 
    @TestProduct1 = ProductID, 
    @OriginalPrice1 = ListPrice
FROM Production.Product 
WHERE ListPrice > 100 AND ListPrice < 500
ORDER BY ProductID;

-- Get second test product  
SELECT TOP 1 
    @TestProduct2 = ProductID, 
    @OriginalPrice2 = ListPrice
FROM Production.Product 
WHERE ListPrice > 500 AND ListPrice < 1000
    AND ProductID != @TestProduct1
ORDER BY ProductID;

PRINT 'Selected test products:';
SELECT 
    ProductID,
    Name,
    ListPrice,
    'Test Product 1' AS Role
FROM Production.Product 
WHERE ProductID = @TestProduct1

UNION ALL

SELECT 
    ProductID,
    Name, 
    ListPrice,
    'Test Product 2' AS Role
FROM Production.Product 
WHERE ProductID = @TestProduct2;

PRINT '';

-- =============================================
-- TEST 1: Valid Price Increase (Should SUCCEED)
-- =============================================
PRINT 'TEST 1: Valid Price Increase (Should SUCCEED)';
PRINT '==============================================';
PRINT 'Attempting to increase price by $50...';

BEGIN TRY
    BEGIN TRANSACTION;
    
    UPDATE Production.Product 
    SET ListPrice = @OriginalPrice1 + 50.00
    WHERE ProductID = @TestProduct1;
    
    COMMIT TRANSACTION;
    
    PRINT '✓ SUCCESS: Price increase was allowed!';
    
    -- Show the result
    SELECT 
        ProductID,
        Name,
        ListPrice AS NewPrice,
        @OriginalPrice1 AS OriginalPrice,
        ListPrice - @OriginalPrice1 AS PriceIncrease
    FROM Production.Product 
    WHERE ProductID = @TestProduct1;
    
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    PRINT '✗ UNEXPECTED ERROR: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- =============================================
-- TEST 2: Invalid Price Decrease (Should FAIL)
-- =============================================
PRINT 'TEST 2: Invalid Price Decrease (Should FAIL)';
PRINT '=============================================';
PRINT 'Attempting to decrease price by $100...';

BEGIN TRY
    BEGIN TRANSACTION;
    
    UPDATE Production.Product 
    SET ListPrice = @OriginalPrice2 - 100.00
    WHERE ProductID = @TestProduct2;
    
    COMMIT TRANSACTION;
    
    PRINT '✗ UNEXPECTED: Price decrease was allowed! (Trigger may not be working)';
    
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    PRINT '✓ SUCCESS: Price decrease was prevented by trigger!';
    PRINT 'Error Message: ' + ERROR_MESSAGE();
    
    -- Verify price wasn't changed
    SELECT 
        ProductID,
        Name,
        ListPrice AS CurrentPrice,
        @OriginalPrice2 AS OriginalPrice,
        'Price unchanged (as expected)' AS Status
    FROM Production.Product 
    WHERE ProductID = @TestProduct2;
END CATCH

PRINT '';

-- =============================================
-- TEST 3: Setting Same Price (Should SUCCEED)
-- =============================================
PRINT 'TEST 3: Setting Same Price (Should SUCCEED)';
PRINT '============================================';
PRINT 'Attempting to set the same price...';

BEGIN TRY
    BEGIN TRANSACTION;
    
    UPDATE Production.Product 
    SET ListPrice = @OriginalPrice2
    WHERE ProductID = @TestProduct2;
    
    COMMIT TRANSACTION;
    
    PRINT '✓ SUCCESS: Setting same price was allowed!';
    
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    PRINT '✗ ERROR: Setting same price failed: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- =============================================
-- TEST 4: Multiple Products with Price Drop (Should FAIL)
-- =============================================
PRINT 'TEST 4: Multiple Products with Price Drop (Should FAIL)';
PRINT '=======================================================';
PRINT 'Attempting to update multiple products with price decreases...';

BEGIN TRY
    BEGIN TRANSACTION;
    
    UPDATE Production.Product 
    SET ListPrice = ListPrice - 50.00
    WHERE ProductID IN (@TestProduct1, @TestProduct2);
    
    COMMIT TRANSACTION;
    
    PRINT '✗ UNEXPECTED: Multiple price decreases were allowed!';
    
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    PRINT '✓ SUCCESS: Multiple price decreases prevented by trigger!';
    PRINT 'Error Message: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- =============================================
-- TEST 5: Non-Price Column Update (Should SUCCEED)
-- =============================================
PRINT 'TEST 5: Non-Price Column Update (Should SUCCEED)';
PRINT '================================================';
PRINT 'Attempting to update ModifiedDate (non-price column)...';

BEGIN TRY
    BEGIN TRANSACTION;
    
    UPDATE Production.Product 
    SET ModifiedDate = GETDATE()
    WHERE ProductID = @TestProduct1;
    
    COMMIT TRANSACTION;
    
    PRINT '✓ SUCCESS: Non-price column update was allowed!';
    
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    PRINT '✗ ERROR: Non-price update failed: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- =============================================
-- CLEANUP: Restore Original Prices
-- =============================================
PRINT 'CLEANUP: Restoring Original Prices';
PRINT '==================================';

BEGIN TRY
    -- Restore original price for test product 1
    UPDATE Production.Product 
    SET ListPrice = @OriginalPrice1
    WHERE ProductID = @TestProduct1;
    
    PRINT '✓ Test Product 1 price restored to original value.';
    
END TRY
BEGIN CATCH
    PRINT '✗ Error restoring price: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- =============================================
-- FINAL SUMMARY
-- =============================================
PRINT 'TESTING SUMMARY';
PRINT '===============';
PRINT 'Your PreventPriceDrop trigger should have:';
PRINT '• ✓ Allowed price increases';
PRINT '• ✓ Prevented price decreases';  
PRINT '• ✓ Allowed setting same price';
PRINT '• ✓ Prevented multiple price decreases';
PRINT '• ✓ Allowed non-price column updates';
PRINT '';
PRINT 'If all tests passed as expected, your trigger is working correctly!';
PRINT '';
PRINT '=============================================';
PRINT 'TESTING COMPLETED';
PRINT '=============================================';
GO
