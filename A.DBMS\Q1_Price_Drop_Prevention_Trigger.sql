USE AdventureWorks2022;
GO

CREATE TRIGGER trg_PreventPriceDrop
ON Production.Product
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    
    IF UPDATE(ListPrice)
    BEGIN
        IF EXISTS (
            SELECT 1 
            FROM inserted i
            INNER JOIN deleted d ON i.ProductID = d.ProductID
            WHERE i.ListPrice < d.ListPrice
                AND d.ListPrice IS NOT NULL  
                AND i.ListPrice IS NOT NULL
        )
        BEGIN
            -- Get details of the problematic updates for error message
            DECLARE @ErrorDetails NVARCHAR(MAX) = '';
            
            SELECT @ErrorDetails = @ErrorDetails + 
                'ProductID: ' + CAST(i.ProductID AS NVARCHAR(10)) + 
                ', Product: ' + ISNULL(i.Name, 'Unknown') +
                ', Current Price: $' + CAST(d.ListPrice AS NVARCHAR(20)) + 
                ', Attempted New Price: $' + CAST(i.ListPrice AS NVARCHAR(20)) + '; '
            FROM inserted i
            INNER JOIN deleted d ON i.ProductID = d.ProductID
            WHERE i.ListPrice < d.ListPrice
                AND d.ListPrice IS NOT NULL
                AND i.ListPrice IS NOT NULL;
            
            -- Rollback the transaction and raise an error
            ROLL<PERSON>CK TRANSACTION;
            
            DECLARE @ErrorMessage NVARCHAR(MAX) = 
                'Price drop prevention: Cannot reduce ListPrice below current value. ' +
                'Affected products: ' + @ErrorDetails;
            
            RAISERROR(@ErrorMessage, 16, 1);
            RETURN;
        END
    END
END;
GO

-- =============================================
-- Display trigger information
-- =============================================
PRINT 'Trigger "trg_PreventPriceDrop" created successfully!';
PRINT 'This trigger will prevent any UPDATE operation that reduces the ListPrice of products.';
GO

-- =============================================
-- View the created trigger
-- =============================================
SELECT 
    t.name AS TriggerName,
    t.type_desc AS TriggerType,
    t.is_disabled AS IsDisabled,
    OBJECT_NAME(t.parent_id) AS TableName
FROM sys.triggers t
WHERE t.name = 'trg_PreventPriceDrop';
GO
